[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:For all spell cost related attributes in the item filtering and display system, invert the color coding logic because negative values are beneficial (lower spell costs are better). This applies to: DESCRIPTION:- 1st Spell Cost (raw and percentage) - 2nd Spell Cost (raw and percentage)  - 3rd Spell Cost (raw and percentage) - 4th Spell Cost (raw and percentage)  Currently, positive values are likely shown in green (good) and negative values in red (bad). This should be reversed for spell costs specifically: - Negative spell cost values should be displayed in green (beneficial - reduces mana cost) - Positive spell cost values should be displayed in red (detrimental - increases mana cost)  This color inversion should be applied consistently across all components that display these spell cost attributes, including item cards, detail modals, tooltips, and any filtering interfaces.
-[x] NAME:Fix two issues with the item details modal in the Wynncraft Item Viewer: DESCRIPTION:1. **Modal positioning issue**: Currently, when a user clicks on an item anywhere on the page, the modal always appears at the top of the viewport instead of being centered. The modal should always be positioned in the center of the current viewport, regardless of where the clicked item is located on the page (whether at the top, middle, or bottom of the scrolled content).  2. **Scroll functionality issue**: After closing/exiting the item details modal, the main item list becomes unscrollable - users can no longer scroll through the list of items. The page scroll functionality should be fully restored when the modal is closed.  Please investigate the ItemDetailsModal.css file and related modal components to identify and fix both the positioning logic and the scroll restoration behavior.
-[x] NAME:Add a hover tooltip to item cards in the Wynncraft Item Viewer that appears when users hover over any item card. The tooltip should display the text "Click to view details" or similar instructional text to inform users that the item cards are clickable and will open detailed information about the item. The tooltip should: DESCRIPTION:1. Appear on hover with a slight delay (e.g., 500ms) to avoid flickering 2. Be positioned appropriately relative to the cursor or card 3. Have consistent styling that matches the application's design theme 4. Disappear when the user stops hovering 5. Be accessible and work across different devices/browsers  The tooltip should be implemented on the item card components that are displayed in the main item listing/grid view.
-[ ] NAME: DESCRIPTION: